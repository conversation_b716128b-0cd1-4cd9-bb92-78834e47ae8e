# Environment variables - NEVER commit these!
.env
.env.local
.env.production

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary files
*.tmp
*.temp
.cache/

# Backup files
*.bak
*.backup

# Node modules (if you add Node.js later)
node_modules/
npm-debug.log

# Build outputs (if you add build process later)
dist/
build/

# Local development files
.local
.env.development

# Database files (if using local SQLite for testing)
*.db
*.sqlite
*.sqlite3
